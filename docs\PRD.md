# 📋 RLPONTO - PRODUCT REQUIREMENTS DOCUMENT (PRD)

## 🎯 VISÃO GERAL DO PRODUTO

### **📊 INFORMAÇÕES BÁSICAS:**
- **Nome do Produto**: RLPONTO - Sistema de Controle de Ponto Eletrônico
- **Versão**: 1.0.0
- **Tipo**: B2B SaaS para Construtoras
- **Modelo de Negócio**: Licenciamento por assinatura (Mensal/Anual/Vitalícia)
- **Público-Alvo**: Construtoras e empresas da construção civil

### **🎯 OBJETIVO DO PRODUTO:**
Desenvolver um sistema completo de controle de ponto eletrônico especializado para o setor da construção civil, oferecendo gestão multi-empresa, integração biométrica, relatórios avançados e conformidade com a legislação trabalhista brasileira.

### **💼 MODELO DE NEGÓCIO:**
- **Estrutura**: Multi-empresa com alocação de funcionários
- **Clientes**: Construtoras (empresa principal)
- **Usuários Finais**: Funcionários da construção civil
- **Administradores**: Departamentos de RH das construtoras
- **Diferencial**: Especialização em obras e canteiros de construção

---

## 👥 PERSONAS E STAKEHOLDERS

### **🏗️ PERSONA 1: GESTOR DE RH DA CONSTRUTORA**
- **Nome**: Maria Silva
- **Cargo**: Gerente de Recursos Humanos
- **Empresa**: Construtora ABC (500+ funcionários)
- **Necessidades**:
  - Controlar ponto de funcionários em múltiplas obras
  - Gerar relatórios para folha de pagamento
  - Monitorar cumprimento de jornadas
  - Gerenciar EPIs e documentos
- **Dores**:
  - Dificuldade para controlar funcionários em obras distantes
  - Relatórios manuais demorados e sujeitos a erros
  - Falta de integração entre sistemas

### **🔧 PERSONA 2: SUPERVISOR DE OBRA**
- **Nome**: João Santos
- **Cargo**: Supervisor de Obra
- **Empresa**: Obra Residencial XYZ
- **Necessidades**:
  - Registrar ponto dos funcionários na obra
  - Acompanhar presença e produtividade
  - Gerenciar turnos e escalas
- **Dores**:
  - Controle manual de presença
  - Falta de visibilidade em tempo real
  - Dificuldade para justificar ausências

### **👷 PERSONA 3: FUNCIONÁRIO DA CONSTRUÇÃO**
- **Nome**: Carlos Oliveira
- **Cargo**: Pedreiro
- **Empresa**: Alocado na Obra ABC
- **Necessidades**:
  - Registrar ponto de forma simples
  - Consultar seus horários e faltas
  - Receber EPIs adequados
- **Dores**:
  - Filas para bater ponto
  - Dificuldade para consultar informações
  - Problemas com biometria (mãos sujas)

---

## 🚀 FUNCIONALIDADES PRINCIPAIS

### **🔐 1. SISTEMA DE AUTENTICAÇÃO E AUTORIZAÇÃO**
#### **Níveis de Acesso:**
- **admin_total**: Acesso completo ao sistema
- **admin_limitado**: Administração sem configurações críticas
- **gerente_rh**: Gestão de funcionários e relatórios
- **supervisor_obra**: Supervisão de obra específica
- **cliente_vinculado**: Acesso apenas aos funcionários alocados
- **operador_biometria**: Operação de dispositivos biométricos
- **readonly**: Apenas visualização

#### **Critérios de Aceitação:**
- ✅ Login seguro com email/senha + 2FA opcional
- ✅ Controle granular de permissões por módulo
- ✅ Sessões com timeout configurável
- ✅ Log completo de acessos e ações

### **🏢 2. GESTÃO DE EMPRESAS**
#### **Empresa Principal (Construtora):**
- Cadastro completo com dados fiscais
- Configuração de jornadas padrão
- Gestão de obras e projetos
- Controle de usuários e permissões

#### **Empresas Clientes:**
- Alocação de funcionários por projeto
- Herança de configurações da empresa principal
- Relatórios específicos por cliente
- Faturamento separado

#### **Critérios de Aceitação:**
- ✅ Cadastro de múltiplas empresas
- ✅ Hierarquia empresa principal → clientes
- ✅ Alocação flexível de funcionários
- ✅ Isolamento de dados entre clientes

### **👥 3. GESTÃO DE FUNCIONÁRIOS**
#### **Cadastro Completo (47 campos em 6 etapas):**
1. **Dados Pessoais**: Nome, CPF, RG, nascimento, etc.
2. **Endereço**: CEP com autocompletar, logradouro completo
3. **Contatos**: Telefones, email, contato emergência
4. **Documentos**: CTPS, PIS, CNH, certificados
5. **Dados Profissionais**: Cargo, salário, admissão, jornada
6. **Dados Bancários**: Conta, PIX, benefícios

#### **Gestão de EPIs:**
- Catálogo com 9 categorias de EPIs
- Controle de entrega e devolução
- Validade e certificações
- Relatórios de conformidade

#### **Critérios de Aceitação:**
- ✅ Formulário multi-etapas com validação
- ✅ Upload de fotos e documentos
- ✅ Integração com APIs governamentais
- ✅ Controle completo de EPIs

### **⏰ 4. REGISTRO DE PONTO**
#### **Tipos de Registro:**
- **B1**: Início da jornada
- **B2**: Saída para intervalo
- **B3**: Retorno do intervalo
- **B4**: Fim da jornada
- **B5**: Início de hora extra
- **B6**: Fim de hora extra

#### **Métodos de Registro:**
- Biométrico (múltiplos fabricantes)
- Manual (com justificativa)
- Aplicativo móvel com geolocalização

#### **Princípio Fundamental:**
**"NUNCA IMPEDIR FUNCIONÁRIO ATIVO DE BATER PONTO"**
- Sistema sempre permite registro
- Classificação posterior pelo RH
- Alertas automáticos para irregularidades

#### **Critérios de Aceitação:**
- ✅ Suporte a todos os tipos de registro
- ✅ Integração com dispositivos biométricos
- ✅ Validação de sequência e horários
- ✅ Sistema de alertas inteligente

### **📊 5. RELATÓRIOS E ANALYTICS**
#### **Relatórios Principais:**
- **Espelho de Ponto**: Individual e coletivo
- **Controle de Jornada**: Horas trabalhadas, extras, faltas
- **Produtividade**: Por obra, equipe, período
- **Conformidade**: Legislação trabalhista, EPIs
- **Financeiro**: Custos por projeto, horas extras

#### **Funcionalidades:**
- Filtros avançados por período, obra, funcionário
- Exportação em PDF, Excel, CSV
- Agendamento automático de relatórios
- Dashboard em tempo real

#### **Critérios de Aceitação:**
- ✅ Relatórios com dados precisos
- ✅ Interface intuitiva para filtros
- ✅ Exportação em múltiplos formatos
- ✅ Performance otimizada para grandes volumes

### **🔬 6. SISTEMA BIOMÉTRICO**
#### **Dispositivos Suportados:**
- Henry, ControlID, ZKTeco, TopData, Generic
- Protocolos: TCP/IP, REST API, ZK Protocol
- Templates com qualidade 0-100

#### **Funcionalidades:**
- Cadastro e gestão de templates
- Sincronização em tempo real
- Monitoramento de dispositivos
- Backup automático de templates

#### **Critérios de Aceitação:**
- ✅ Integração com múltiplos fabricantes
- ✅ Qualidade de templates controlada
- ✅ Sincronização confiável
- ✅ Monitoramento em tempo real

### **⚙️ 7. CONFIGURAÇÕES DO SISTEMA**
#### **Níveis de Configuração:**
1. **Sistema**: Configurações globais
2. **Empresa Principal**: Padrões da construtora
3. **Empresa Cliente**: Específicas do projeto
4. **Usuário**: Preferências pessoais

#### **Parâmetros Principais:**
- Tolerâncias de atraso/saída antecipada
- Jornadas e turnos
- Integração com APIs externas
- Notificações e alertas

#### **Critérios de Aceitação:**
- ✅ Hierarquia de configurações respeitada
- ✅ Interface intuitiva para parâmetros
- ✅ Validação de valores
- ✅ Backup automático de configurações

### **🔗 8. APIs E INTEGRAÇÕES**
#### **APIs Públicas:**
- Autenticação JWT
- CRUD de funcionários
- Registros de ponto
- Relatórios
- Webhooks

#### **Integrações:**
- Sistemas de folha de pagamento
- ERPs da construção civil
- Dispositivos biométricos
- APIs governamentais (eSocial, FGTS)

#### **Critérios de Aceitação:**
- ✅ Documentação completa da API
- ✅ Rate limiting e segurança
- ✅ Webhooks confiáveis
- ✅ SDKs para linguagens principais

---

## 🎨 DESIGN E EXPERIÊNCIA DO USUÁRIO

### **🎯 PRINCÍPIOS DE DESIGN:**
1. **Mobile First**: Responsivo para todos os dispositivos
2. **Simplicidade**: Interface intuitiva para usuários não técnicos
3. **Acessibilidade**: WCAG 2.1 AA compliance
4. **Performance**: Carregamento rápido mesmo em conexões lentas

### **🎨 DESIGN SYSTEM:**
- **Tipografia**: Geist Sans/Mono
- **Cores Primárias**: 
  - Teal: #4fbdba (RLPONTO brand)
  - Blue: #667eea (accent)
- **Grid**: Sistema de 8px
- **Componentes**: Baseados em Radix UI + shadcn/ui

### **📱 RESPONSIVIDADE:**
- **Desktop**: 1920px+ (dashboard completo)
- **Tablet**: 768px-1919px (layout adaptado)
- **Mobile**: 320px-767px (interface simplificada)

---

## 🔒 SEGURANÇA E COMPLIANCE

### **🛡️ SEGURANÇA:**
- **Autenticação**: JWT + 2FA opcional
- **Criptografia**: AES-256 para dados sensíveis
- **HTTPS**: TLS 1.3 obrigatório
- **Backup**: Automático com criptografia
- **Auditoria**: Log completo de todas as ações

### **📋 COMPLIANCE:**
- **LGPD**: Proteção de dados pessoais
- **CLT**: Conformidade trabalhista
- **eSocial**: Integração obrigatória
- **Portaria 671/2021**: Controle de ponto eletrônico

---

## 📈 MÉTRICAS E KPIs

### **📊 MÉTRICAS DE PRODUTO:**
- **Adoção**: Usuários ativos mensais
- **Engagement**: Registros de ponto por dia
- **Performance**: Tempo de resposta < 200ms
- **Disponibilidade**: Uptime > 99.5%

### **💰 MÉTRICAS DE NEGÓCIO:**
- **MRR**: Receita recorrente mensal
- **Churn**: Taxa de cancelamento < 5%
- **NPS**: Net Promoter Score > 50
- **CAC**: Custo de aquisição de cliente

---

## 🚀 ROADMAP E PRIORIZAÇÃO

### **📅 FASE 1 - MVP (3 meses):**
- ✅ Autenticação e autorização
- ✅ Gestão básica de funcionários
- ✅ Registro de ponto manual
- ✅ Relatórios essenciais

### **📅 FASE 2 - CORE (6 meses):**
- ✅ Integração biométrica
- ✅ Gestão de EPIs
- ✅ APIs públicas
- ✅ Dashboard avançado

### **📅 FASE 3 - ADVANCED (9 meses):**
- ✅ Aplicativo móvel
- ✅ Integrações ERP
- ✅ Analytics avançados
- ✅ Automações

### **📅 FASE 4 - ENTERPRISE (12 meses):**
- ✅ Multi-tenancy completo
- ✅ Customizações por cliente
- ✅ Suporte 24/7
- ✅ Certificações de segurança

---

## 🔧 CONSIDERAÇÕES TÉCNICAS

### **🏗️ ARQUITETURA:**
- **Frontend**: Next.js 14.2+ com App Router + TypeScript 5.0+
- **Backend**: API Routes do Next.js + Prisma 5.0+ ORM
- **Banco de Dados**: PostgreSQL 15+ (produção) / SQLite (desenvolvimento)
- **Autenticação**: NextAuth.js v5 (Auth.js) - latest stable
- **UI**: Tailwind CSS v3 + Radix UI + shadcn/ui
- **State**: Zustand (global) + React Hook Form (formulários)
- **Cache**: Redis 7+
- **Deploy**: Systemd Service (Linux) com auto-start robusto

### **🔒 SEGURANÇA:**
- Criptografia de senhas com bcrypt
- Tokens JWT para sessões
- Validação de entrada em todas as APIs
- Logs de auditoria completos
- Backup automático dos dados

### **⚡ PERFORMANCE:**
- Cache de consultas frequentes
- Otimização de imagens
- Lazy loading de componentes
- Compressão de assets
- CDN para arquivos estáticos

### **🚀 AUTO-START E RESILIÊNCIA DO SISTEMA**

#### **🎯 Implementação de Auto-Start Robusto**
Para evitar erros "502 Bad Gateway" e garantir que o sistema RLPONTO inicie automaticamente após reinicializações do servidor, é **OBRIGATÓRIA** a implementação de um serviço systemd dedicado.

#### **⚙️ Configuração do Serviço Systemd**
```bash
# Criar serviço systemd SEGURO para RLPONTO
cat > /etc/systemd/system/rlponto.service << EOF
[Unit]
Description=RLPONTO - Sistema de Controle de Ponto Eletrônico
Documentation=https://docs.rlponto.com
After=network-online.target postgresql.service redis.service
Wants=network-online.target postgresql.service redis.service
Requires=postgresql.service

[Service]
Type=simple
User=rlponto
Group=rlponto
WorkingDirectory=/opt/rlponto
ExecStart=/usr/bin/node server.js
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Variáveis de ambiente (usar arquivo separado)
EnvironmentFile=/etc/rlponto/environment
Environment=NODE_ENV=production
Environment=PORT=3000

# Logs estruturados
StandardOutput=journal
StandardError=journal
SyslogIdentifier=rlponto

# Segurança avançada
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
PrivateTmp=true
ProtectHostname=true
ProtectClock=true
ProtectKernelLogs=true
ProtectProc=invisible
ProcSubset=pid
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# Limites de recursos
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# Diretórios com permissões específicas
ReadWritePaths=/opt/rlponto/logs /opt/rlponto/uploads /opt/rlponto/temp
ReadOnlyPaths=/opt/rlponto
BindReadOnlyPaths=/etc/ssl/certs

# Capabilities mínimas necessárias
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

[Install]
WantedBy=multi-user.target
EOF

# Criar usuário dedicado (SEGURANÇA)
useradd -r -s /bin/false -d /opt/rlponto -c "RLPONTO Service User" rlponto

# Criar diretórios e configurar permissões
mkdir -p /opt/rlponto/{logs,uploads,temp}
mkdir -p /etc/rlponto/certs
chown -R rlponto:rlponto /opt/rlponto
chmod 750 /opt/rlponto
chmod 700 /etc/rlponto

# Criar arquivo de ambiente seguro
cat > /etc/rlponto/environment << 'ENVEOF'
DATABASE_URL=postgresql://rlponto_user:SENHA_COMPLEXA@localhost:5432/rlponto_db
REDIS_URL=redis://localhost:6379/0
AUTH_SECRET=CHAVE_256_BITS_SUPER_SEGURA
AUTH_URL=http://***********
SSL_CERT_PATH=/etc/ssl/certs/rlponto.crt
SSL_KEY_PATH=/etc/ssl/private/rlponto.key
LOG_LEVEL=info
ENVEOF

chmod 600 /etc/rlponto/environment

# Habilitar auto-start
systemctl daemon-reload
systemctl enable rlponto.service
systemctl start rlponto.service
```

#### **✅ Validações Obrigatórias**
```bash
# Verificar se está habilitado
systemctl is-enabled rlponto.service  # DEVE retornar "enabled"

# Verificar status
systemctl status rlponto.service      # DEVE mostrar "active (running)"

# Testar aplicação localmente
curl -I http://localhost:3000          # DEVE retornar HTTP 200/307

# Testar acesso via proxy
curl -I http://***********             # DEVE retornar HTTP 200 via Nginx

# TESTE CRÍTICO - Reinicialização
sudo reboot
# Após reinicialização, aguardar 2-3 minutos e testar:
curl -I http://***********             # DEVE funcionar SEM intervenção manual
```

#### **📋 Checklist de Resiliência**
- ✅ Serviço rlponto.service criado e configurado
- ✅ Dependência do PostgreSQL configurada
- ✅ Variáveis de ambiente do banco configuradas
- ✅ Auto-start habilitado com systemctl enable
- ✅ Restart automático configurado (Restart=always)
- ✅ Logs centralizados no systemd journal
- ✅ Testado com reinicialização completa do sistema
- ✅ Aplicação inicia automaticamente após reboot
- ✅ Nginx consegue conectar sem erro 502

#### **🔍 Troubleshooting**
Se encontrar erro 502 após reinicialização:
```bash
# Verificar status do serviço
systemctl status rlponto.service

# Ver logs em tempo real
journalctl -u rlponto.service -f

# Verificar se porta está em uso
ss -tlnp | grep :3000

# Testar aplicação diretamente
curl -I http://localhost:3000

# Testar via proxy Nginx
curl -I http://***********
```

**⚠️ IMPORTANTE**: Um sistema em produção NUNCA deve depender de intervenção manual para funcionar após reinicializações. A implementação de auto-start robusto é CRÍTICA para a confiabilidade do sistema RLPONTO.

---

## 💳 SISTEMA DE LICENCIAMENTO

### **📋 PLANOS DISPONÍVEIS:**

#### **🔵 PLANO BÁSICO - R$ 299/mês:**
- **Funcionários**: Até 50
- **Usuários Sistema**: 2
- **Dispositivos Biométricos**: 1
- **Empresas/Projetos**: 3
- **Relatórios**: Básicos (5 tipos)
- **APIs**: Não disponível
- **Armazenamento**: 1GB
- **Suporte**: Email (48h)
- **Backup**: Semanal

#### **🟡 PLANO PROFISSIONAL - R$ 599/mês:**
- **Funcionários**: Até 200
- **Usuários Sistema**: 5
- **Dispositivos Biométricos**: 3
- **Empresas/Projetos**: 10
- **Relatórios**: Completos (15 tipos)
- **APIs**: 1.000 requests/dia
- **Armazenamento**: 5GB
- **Suporte**: Prioritário (24h)
- **Backup**: Diário

#### **🟢 PLANO EMPRESARIAL - R$ 1.299/mês:**
- **Funcionários**: Ilimitados
- **Usuários Sistema**: Ilimitados
- **Dispositivos Biométricos**: Ilimitados
- **Empresas/Projetos**: Ilimitados
- **Relatórios**: Todos + Personalizados
- **APIs**: 10.000 requests/dia
- **Armazenamento**: 50GB
- **Suporte**: 24/7 + WhatsApp
- **Backup**: Tempo real
- **Customizações**: Incluídas

#### **⚫ PLANO VITALÍCIO - R$ 15.000 (único):**
- **Funcionários**: Até 100
- **Usuários Sistema**: 3
- **Dispositivos Biométricos**: 2
- **Empresas/Projetos**: 5
- **Relatórios**: Básicos + Gerenciais
- **APIs**: Não disponível
- **Armazenamento**: 2GB
- **Suporte**: Email (72h)
- **Backup**: Semanal
- **Atualizações**: 2 anos incluídos

### **🔧 CONTROLE TÉCNICO:**

#### **📡 API DE VALIDAÇÃO:**
- **Endpoint**: `https://api.rlponto.com/v1/licenses`
- **Método**: POST
- **Headers**: `Authorization: Bearer {license_key}`
- **Payload**: `{action: "validate", resource: "funcionarios", current_count: 45}`
- **Response**: `{valid: true, limit: 50, remaining: 5, expires_at: "2024-12-31"}`

#### **⚡ VALIDAÇÃO EM TEMPO REAL:**
- Verificação a cada ação crítica
- Cache local de 5 minutos
- Fallback offline por 24h
- Sincronização automática

#### **🚨 CONTROLE DE LIMITES:**
- **Soft Limit (90%)**: Aviso amarelo
- **Hard Limit (100%)**: Bloqueio de novas ações
- **Degradação**: Funcionalidades reduzidas
- **Alertas**: 30, 15, 7 dias antes do vencimento
