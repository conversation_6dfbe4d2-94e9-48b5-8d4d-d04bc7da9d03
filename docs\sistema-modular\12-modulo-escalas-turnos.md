# ⏰ MÓDULO DE ESCALAS E TURNOS

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**
- Gerenciar jornadas de trabalho flexíveis
- Controlar turnos e escalas de funcionários
- Automatizar rotação de escalas
- Calcular horas extras e adicionais
- Garantir conformidade com legislação trabalhista

### **📊 RESPONSABILIDADES:**
- CRUD de jornadas de trabalho
- Gestão de turnos (diurno, noturno, misto)
- Planejamento e controle de escalas
- Cálculo automático de adicionais
- Relatórios de conformidade
- Integração com registro de ponto

---

## 🚀 FUNCIONALIDADES

### **✅ FUNCIONALIDADES PRINCIPAIS:**

#### **1. GESTÃO DE JORNADAS:**
- <PERSON><PERSON><PERSON> pad<PERSON> (8h, 6h, 12x36)
- Jornadas flexíveis personalizáveis
- Tolerâncias de entrada/saída
- Intervalos obrigatórios
- Configuração por empresa/setor

#### **2. GESTÃO DE TURNOS:**
- Turnos diurnos, noturnos e mistos
- Adicional noturno automático
- Turnos que cruzam meia-noite
- Percentuais de adicional
- Controle de vigência

#### **3. PLANEJAMENTO DE ESCALAS:**
- Escalas fixas e rotativas
- Planejamento mensal/semanal
- Substituições automáticas
- Conflitos de horários
- Histórico de alterações

#### **4. CÁLCULOS AUTOMÁTICOS:**
- Horas extras (50% e 100%)
- Adicional noturno (20%)
- Horas em feriados
- Banco de horas
- Descanso semanal remunerado

---

## 📱 TELAS E INTERFACES

### **1. DASHBOARD DE ESCALAS** (`/escalas`)

```typescript
interface DashboardEscalasScreen {
  resumo_geral: {
    funcionarios_escalados: number;
    turnos_ativos: number;
    conflitos_pendentes: number;
    horas_extras_mes: number;
  };
  
  calendario_escalas: {
    visualizacao: "mensal" | "semanal" | "diaria";
    filtros: {
      empresa: string[];
      setor: string[];
      turno: string[];
      funcionario: string;
    };
    
    eventos: {
      funcionario_id: string;
      nome_funcionario: string;
      turno: string;
      horario_inicio: string;
      horario_fim: string;
      cor_turno: string;
      status: "confirmado" | "pendente" | "conflito";
    }[];
  };
  
  alertas_importantes: {
    conflitos_horario: {
      funcionario: string;
      data: Date;
      turnos_conflitantes: string[];
    }[];
    
    funcionarios_sem_escala: {
      funcionario: string;
      ultimo_turno: Date;
      dias_sem_escala: number;
    }[];
    
    limites_horas_extras: {
      funcionario: string;
      horas_extras_mes: number;
      limite_legal: number;
      percentual_usado: number;
    }[];
  };
}
```

### **2. GESTÃO DE JORNADAS** (`/escalas/jornadas`)

```typescript
interface GestaoJornadasScreen {
  lista_jornadas: {
    id: string;
    nome: string;
    tipo: "normal" | "flexivel" | "escala";
    carga_semanal: number; // horas
    empresas_vinculadas: number;
    funcionarios_ativos: number;
    status: "ativa" | "inativa";
  }[];
  
  formulario_jornada: {
    dados_basicos: {
      nome: string;
      tipo_jornada: "normal" | "flexivel" | "escala";
      carga_horaria_semanal: number;
      empresa_id: string;
    };
    
    configuracao_horarios: {
      dias_semana: {
        segunda: { ativo: boolean; entrada: string; saida: string; intervalo: number };
        terca: { ativo: boolean; entrada: string; saida: string; intervalo: number };
        quarta: { ativo: boolean; entrada: string; saida: string; intervalo: number };
        quinta: { ativo: boolean; entrada: string; saida: string; intervalo: number };
        sexta: { ativo: boolean; entrada: string; saida: string; intervalo: number };
        sabado: { ativo: boolean; entrada: string; saida: string; intervalo: number };
        domingo: { ativo: boolean; entrada: string; saida: string; intervalo: number };
      };
    };
    
    tolerancias: {
      entrada_minutos: number;
      saida_minutos: number;
      intervalo_minimo: number;
      intervalo_maximo: number;
    };
    
    calculos_automaticos: {
      horas_extras_apos: number; // minutos
      adicional_noturno_inicio: string; // "22:00"
      adicional_noturno_fim: string; // "05:00"
      percentual_he_normal: number; // 50
      percentual_he_feriado: number; // 100
      percentual_adicional_noturno: number; // 20
    };
  };
}
```

### **3. GESTÃO DE TURNOS** (`/escalas/turnos`)

```typescript
interface GestaoTurnosScreen {
  lista_turnos: {
    id: string;
    nome: string;
    horario_inicio: string;
    horario_fim: string;
    tipo: "diurno" | "noturno" | "misto";
    adicional_noturno: boolean;
    funcionarios_escalados: number;
    status: "ativo" | "inativo";
  }[];
  
  formulario_turno: {
    dados_basicos: {
      nome: string;
      empresa_id: string;
      horario_inicio: string;
      horario_fim: string;
      cruza_meia_noite: boolean;
    };
    
    adicionais: {
      adicional_noturno: boolean;
      percentual_adicional: number;
      horario_inicio_noturno: string;
      horario_fim_noturno: string;
    };
    
    configuracoes: {
      intervalo_obrigatorio: boolean;
      duracao_intervalo: number; // minutos
      tolerancia_entrada: number; // minutos
      tolerancia_saida: number; // minutos
    };
  };
}
```

---

## 🔄 INTEGRAÇÕES

### **1. MÓDULO DE FUNCIONÁRIOS:**
- Vinculação de jornadas padrão
- Histórico de alterações de jornada
- Validação de dados contratuais

### **2. MÓDULO DE REGISTRO DE PONTO:**
- Validação automática de horários
- Cálculo de horas extras
- Aplicação de tolerâncias
- Detecção de irregularidades

### **3. MÓDULO DE RELATÓRIOS:**
- Relatórios de escalas
- Análise de horas extras
- Conformidade trabalhista
- Custos por turno

### **4. MÓDULO DE EMPRESAS:**
- Herança de configurações
- Jornadas por projeto
- Customizações específicas

---

## 🔧 REGRAS DE NEGÓCIO

### **1. VALIDAÇÕES OBRIGATÓRIAS:**
- Jornada não pode exceder 44h semanais (CLT)
- Intervalo mínimo de 1h para jornadas > 6h
- Descanso mínimo de 11h entre jornadas
- Máximo 2h extras por dia
- Adicional noturno entre 22h e 5h

### **2. CÁLCULOS AUTOMÁTICOS:**
- HE 50% para primeiras 2h extras
- HE 100% para horas excedentes
- Adicional noturno 20% sobre hora normal
- Banco de horas com limite de 10h

### **3. ALERTAS AUTOMÁTICOS:**
- Conflitos de escala
- Limites de horas extras
- Funcionários sem escala
- Violações trabalhistas

---

## 📊 MÉTRICAS E KPIs

### **1. INDICADORES OPERACIONAIS:**
- Taxa de ocupação por turno
- Média de horas extras por funcionário
- Conflitos de escala resolvidos
- Tempo médio de planejamento

### **2. INDICADORES FINANCEIROS:**
- Custo total de horas extras
- Economia com otimização de escalas
- Custo por hora trabalhada
- ROI do planejamento automático

### **3. INDICADORES DE CONFORMIDADE:**
- % de conformidade com CLT
- Violações trabalhistas detectadas
- Tempo de correção de irregularidades
- Auditoria de escalas aprovadas

---

## 🔒 SEGURANÇA E AUDITORIA

### **1. CONTROLE DE ACESSO:**
- Apenas gerentes podem alterar jornadas
- Supervisores podem ajustar escalas
- Funcionários visualizam apenas suas escalas
- Log completo de alterações

### **2. INTEGRIDADE DOS DADOS:**
- Validação de horários consistentes
- Prevenção de conflitos automática
- Backup de configurações críticas
- Versionamento de alterações

### **3. COMPLIANCE:**
- Conformidade com CLT
- Relatórios para fiscalização
- Documentação de exceções
- Auditoria de cálculos
