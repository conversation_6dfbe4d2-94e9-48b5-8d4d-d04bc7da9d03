# 🏗️ RLPONTO - ARCHITECTURE DOCUMENT

## 🎯 VISÃO GERAL DA ARQUITETURA

### **📊 STACK TECNOLÓGICO:**
```typescript
interface TechStack {
  frontend: {
    framework: "Next.js 14.2+ with App Router";
    language: "TypeScript 5.0+";
    styling: "Tailwind CSS v3";
    ui_components: "Radix UI + shadcn/ui";
    state_management: "Zustand";
    forms: "React Hook Form + Zod";
    animations: "Framer Motion";
  };

  backend: {
    runtime: "Node.js 20+";
    framework: "Next.js API Routes";
    database: "PostgreSQL 15+";
    orm: "Prisma 5.0+";
    authentication: "NextAuth.js v5 (Auth.js) - latest stable";
    validation: "Zod";
    encryption: "bcryptjs + crypto";
  };
  
  infrastructure: {
    deployment: "Systemd Service (Linux) com auto-start robusto";
    database_hosting: "Self-hosted PostgreSQL / Managed PostgreSQL";
    file_storage: "Local Storage + Cloud Backup";
    monitoring: "Sentry + Custom Analytics + Systemd Journal";
    backup: "Automated daily backups";
  };
}
```

### **🏗️ ARQUITETURA GERAL:**
```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
├─────────────────────────────────────────────────────────────┤
│  Next.js App Router  │  React Components  │  Tailwind CSS   │
│  TypeScript          │  Zustand Store     │  Radix UI       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     BUSINESS LAYER                          │
├─────────────────────────────────────────────────────────────┤
│  API Routes          │  Authentication    │  Validation     │
│  Business Logic      │  Authorization     │  Error Handling │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      DATA LAYER                             │
├─────────────────────────────────────────────────────────────┤
│  Prisma ORM          │  PostgreSQL        │  File Storage   │
│  Database Schema     │  Migrations        │  Backup System  │
└─────────────────────────────────────────────────────────────┘
```

---

## 📁 ESTRUTURA DO PROJETO

### **🗂️ ORGANIZAÇÃO DE DIRETÓRIOS:**
```
src/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Grupo de rotas de autenticação
│   │   ├── login/
│   │   ├── register/
│   │   └── layout.tsx
│   ├── (dashboard)/              # Grupo de rotas do dashboard
│   │   ├── admin/                # Rotas administrativas
│   │   │   ├── empresas/
│   │   │   ├── funcionarios/
│   │   │   ├── configuracoes/
│   │   │   └── relatorios/
│   │   ├── supervisor/           # Rotas do supervisor
│   │   ├── operador/             # Rotas do operador
│   │   └── layout.tsx
│   ├── api/                      # API Routes
│   │   ├── auth/
│   │   ├── funcionarios/
│   │   ├── ponto/
│   │   ├── relatorios/
│   │   ├── biometria/
│   │   └── webhooks/
│   ├── globals.css
│   ├── layout.tsx                # Layout raiz
│   └── page.tsx                  # Página inicial
├── components/                   # Componentes React
│   ├── ui/                       # Componentes base (shadcn/ui)
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── dialog.tsx
│   │   └── ...
│   ├── layout/                   # Componentes de layout
│   │   ├── header.tsx
│   │   ├── sidebar.tsx
│   │   ├── navigation.tsx
│   │   └── footer.tsx
│   ├── features/                 # Componentes por funcionalidade
│   │   ├── auth/
│   │   ├── funcionarios/
│   │   ├── ponto/
│   │   ├── relatorios/
│   │   └── biometria/
│   └── common/                   # Componentes comuns
├── lib/                          # Bibliotecas e utilitários
│   ├── auth.ts                   # Configuração NextAuth
│   ├── prisma.ts                 # Cliente Prisma
│   ├── store.ts                  # Stores Zustand
│   ├── utils.ts                  # Funções utilitárias
│   ├── validations.ts            # Schemas Zod
│   ├── constants.ts              # Constantes do sistema
│   └── types.ts                  # Tipos TypeScript
├── hooks/                        # Custom hooks
│   ├── use-auth.ts
│   ├── use-funcionarios.ts
│   ├── use-ponto.ts
│   └── use-relatorios.ts
├── styles/                       # Estilos globais
│   └── globals.css
└── types/                        # Definições de tipos
    ├── auth.ts
    ├── funcionarios.ts
    ├── ponto.ts
    └── database.ts
```

---

## 🗄️ ARQUITETURA DO BANCO DE DADOS

### **📊 SCHEMA PRINCIPAL:**
```sql
-- ✅ AUTENTICAÇÃO E USUÁRIOS
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255),
  nome VARCHAR(255) NOT NULL,
  nivel_acesso VARCHAR(50) NOT NULL,
  ativo BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- ✅ EMPRESAS
CREATE TABLE empresas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tipo_empresa VARCHAR(20) NOT NULL, -- 'principal', 'cliente'
  nome_fantasia VARCHAR(255) NOT NULL,
  razao_social VARCHAR(255) NOT NULL,
  cnpj VARCHAR(18) UNIQUE NOT NULL,
  inscricao_estadual VARCHAR(50),
  endereco JSONB NOT NULL,
  contatos JSONB NOT NULL,
  configuracoes JSONB,
  empresa_principal_id UUID REFERENCES empresas(id),
  ativo BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ FUNCIONÁRIOS
CREATE TABLE funcionarios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  empresa_id UUID NOT NULL REFERENCES empresas(id),
  
  -- Dados Pessoais
  nome_completo VARCHAR(255) NOT NULL,
  cpf VARCHAR(14) UNIQUE NOT NULL,
  rg VARCHAR(20),
  data_nascimento DATE,
  sexo VARCHAR(10),
  estado_civil VARCHAR(20),
  
  -- Endereço
  endereco JSONB NOT NULL,
  
  -- Contatos
  telefone_principal VARCHAR(20),
  telefone_secundario VARCHAR(20),
  email VARCHAR(255),
  contato_emergencia JSONB,
  
  -- Documentos
  documentos JSONB,
  
  -- Dados Profissionais
  matricula VARCHAR(50) UNIQUE,
  cargo VARCHAR(100) NOT NULL,
  setor VARCHAR(100),
  data_admissao DATE NOT NULL,
  data_demissao DATE,
  salario DECIMAL(10,2),
  jornada_id UUID REFERENCES jornadas_trabalho(id),
  turno_id UUID REFERENCES turnos(id),
  
  -- Dados Bancários
  dados_bancarios JSONB,
  
  -- Biometria
  template_biometrico TEXT,
  qualidade_template INTEGER,
  
  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- ✅ REGISTROS DE PONTO
CREATE TABLE registros_ponto (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  
  -- Dados do Registro
  data_hora TIMESTAMP NOT NULL,
  tipo_registro VARCHAR(20) NOT NULL, -- B1, B2, B3, B4, B5, B6
  metodo_registro VARCHAR(20) NOT NULL, -- 'biometrico', 'manual'
  
  -- Localização
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  endereco_registro TEXT,
  
  -- Validação
  sequencia_valida BOOLEAN DEFAULT TRUE,
  dentro_jornada BOOLEAN DEFAULT TRUE,
  classificacao VARCHAR(50), -- 'normal', 'atraso', 'saida_antecipada', etc.
  
  -- Justificativa
  observacoes TEXT,
  justificativa TEXT,
  aprovado_por UUID REFERENCES users(id),
  aprovado_em TIMESTAMP,
  
  -- Dispositivo
  dispositivo_id VARCHAR(100),
  ip_origem INET,
  
  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- ✅ EPIs
CREATE TABLE epis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nome VARCHAR(255) NOT NULL,
  categoria VARCHAR(100) NOT NULL,
  descricao TEXT,
  numero_ca VARCHAR(20),
  validade_ca DATE,
  ativo BOOLEAN DEFAULT TRUE
);

CREATE TABLE funcionario_epis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  epi_id UUID NOT NULL REFERENCES epis(id),
  data_entrega DATE NOT NULL,
  data_devolucao DATE,
  quantidade INTEGER DEFAULT 1,
  observacoes TEXT,
  entregue_por UUID REFERENCES users(id),
  recebido_por UUID REFERENCES users(id)
);

-- ✅ DISPOSITIVOS BIOMÉTRICOS
CREATE TABLE dispositivos_biometricos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nome VARCHAR(255) NOT NULL,
  fabricante VARCHAR(100) NOT NULL,
  modelo VARCHAR(100),
  numero_serie VARCHAR(100),
  ip_address INET,
  porta INTEGER,
  protocolo VARCHAR(50),
  status VARCHAR(20) DEFAULT 'ativo',
  ultima_sincronizacao TIMESTAMP,
  configuracoes JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ LICENÇA ATIVA
CREATE TABLE licenca_ativa (
  id SERIAL PRIMARY KEY,
  chave_licenca VARCHAR(29) UNIQUE NOT NULL,
  tipo_licenca VARCHAR(20) NOT NULL,
  nivel_plano VARCHAR(20) NOT NULL,
  data_inicio DATE NOT NULL,
  data_expiracao DATE,
  limites JSONB NOT NULL,
  status_licenca VARCHAR(20) DEFAULT 'ativa',
  instalada_em TIMESTAMP DEFAULT NOW()
);

-- ✅ JORNADAS DE TRABALHO
CREATE TABLE jornadas_trabalho (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nome VARCHAR(255) NOT NULL,
  empresa_id UUID NOT NULL REFERENCES empresas(id),
  tipo_jornada VARCHAR(50) NOT NULL, -- 'normal', 'flexivel', 'escala'
  carga_horaria_semanal INTEGER NOT NULL, -- em minutos
  dias_semana JSONB NOT NULL, -- [1,2,3,4,5] = seg-sex
  horarios JSONB NOT NULL, -- {entrada: "08:00", saida: "17:00", intervalo: 60}
  tolerancia_entrada INTEGER DEFAULT 10, -- minutos
  tolerancia_saida INTEGER DEFAULT 10, -- minutos
  ativo BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ TURNOS
CREATE TABLE turnos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nome VARCHAR(255) NOT NULL,
  empresa_id UUID NOT NULL REFERENCES empresas(id),
  horario_inicio TIME NOT NULL,
  horario_fim TIME NOT NULL,
  cruza_meia_noite BOOLEAN DEFAULT FALSE,
  adicional_noturno BOOLEAN DEFAULT FALSE,
  percentual_adicional DECIMAL(5,2) DEFAULT 0,
  ativo BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ ESCALAS
CREATE TABLE escalas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  turno_id UUID NOT NULL REFERENCES turnos(id),
  data_inicio DATE NOT NULL,
  data_fim DATE,
  dias_semana JSONB NOT NULL, -- [1,2,3,4,5,6,7]
  observacoes TEXT,
  ativo BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ CONFIGURAÇÕES DO SISTEMA
CREATE TABLE configuracoes_sistema (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chave VARCHAR(255) UNIQUE NOT NULL,
  valor JSONB NOT NULL,
  tipo VARCHAR(50) NOT NULL, -- 'sistema', 'empresa', 'usuario'
  empresa_id UUID REFERENCES empresas(id),
  usuario_id UUID REFERENCES users(id),
  descricao TEXT,
  editavel BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- ✅ NOTIFICAÇÕES
CREATE TABLE notificacoes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  usuario_id UUID NOT NULL REFERENCES users(id),
  tipo VARCHAR(50) NOT NULL, -- 'info', 'warning', 'error', 'success'
  titulo VARCHAR(255) NOT NULL,
  mensagem TEXT NOT NULL,
  lida BOOLEAN DEFAULT FALSE,
  data_leitura TIMESTAMP,
  link_acao VARCHAR(500),
  dados_contexto JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ AFASTAMENTOS
CREATE TABLE afastamentos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  tipo_afastamento VARCHAR(50) NOT NULL, -- 'ferias', 'licenca_medica', 'licenca_maternidade', etc.
  data_inicio DATE NOT NULL,
  data_fim DATE NOT NULL,
  dias_corridos INTEGER NOT NULL,
  dias_uteis INTEGER NOT NULL,
  motivo TEXT,
  documento_comprobatorio VARCHAR(500),
  aprovado_por UUID REFERENCES users(id),
  aprovado_em TIMESTAMP,
  status VARCHAR(20) DEFAULT 'pendente', -- 'pendente', 'aprovado', 'rejeitado'
  observacoes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ BANCO DE HORAS
CREATE TABLE banco_horas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  data_referencia DATE NOT NULL,
  saldo_anterior INTEGER DEFAULT 0, -- em minutos
  credito INTEGER DEFAULT 0, -- em minutos
  debito INTEGER DEFAULT 0, -- em minutos
  saldo_atual INTEGER NOT NULL, -- em minutos
  limite_maximo INTEGER DEFAULT 600, -- 10h em minutos
  observacoes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ AUDITORIA (IMUTÁVEL)
CREATE TABLE logs_auditoria (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  usuario_id UUID REFERENCES users(id),
  acao VARCHAR(100) NOT NULL,
  tabela_afetada VARCHAR(100),
  registro_id UUID,
  dados_anteriores JSONB,
  dados_novos JSONB,
  ip_origem INET,
  user_agent TEXT,
  hash_integridade VARCHAR(64) NOT NULL, -- SHA-256
  timestamp TIMESTAMP DEFAULT NOW()
);

-- ✅ TORNAR LOGS IMUTÁVEIS
CREATE OR REPLACE RULE no_update_logs AS ON UPDATE TO logs_auditoria DO INSTEAD NOTHING;
CREATE OR REPLACE RULE no_delete_logs AS ON DELETE TO logs_auditoria DO INSTEAD NOTHING;

-- ✅ ÍNDICES PARA PERFORMANCE
CREATE INDEX idx_funcionarios_empresa ON funcionarios(empresa_id);
CREATE INDEX idx_funcionarios_cpf ON funcionarios(cpf);
CREATE INDEX idx_funcionarios_ativo ON funcionarios(ativo);
CREATE INDEX idx_registros_ponto_funcionario_data ON registros_ponto(funcionario_id, data_hora);
CREATE INDEX idx_registros_ponto_data ON registros_ponto(data_hora);
CREATE INDEX idx_logs_auditoria_usuario_timestamp ON logs_auditoria(usuario_id, timestamp);
CREATE INDEX idx_logs_auditoria_tabela_timestamp ON logs_auditoria(tabela_afetada, timestamp);
CREATE INDEX idx_notificacoes_usuario_lida ON notificacoes(usuario_id, lida);
CREATE INDEX idx_afastamentos_funcionario_periodo ON afastamentos(funcionario_id, data_inicio, data_fim);
CREATE INDEX idx_banco_horas_funcionario_data ON banco_horas(funcionario_id, data_referencia);
```

---

## 🔐 ARQUITETURA DE SEGURANÇA

### **🛡️ AUTENTICAÇÃO E AUTORIZAÇÃO:**
```typescript
interface SecurityArchitecture {
  authentication: {
    provider: "NextAuth.js v5 (Auth.js) - latest stable";
    strategies: [
      "Credentials (email/password)",
      "OAuth (Google, GitHub)",
      "JWT tokens"
    ];
    session_management: "JWT with secure cookies";
    password_hashing: "bcryptjs with salt rounds 12";
  };
  
  authorization: {
    model: "Role-Based Access Control (RBAC)";
    levels: [
      "admin_total",
      "admin_limitado", 
      "gerente_rh",
      "supervisor_obra",
      "cliente_vinculado",
      "operador_biometria",
      "readonly"
    ];
    middleware: "Next.js middleware for route protection";
  };
  
  data_protection: {
    encryption_at_rest: "PostgreSQL TDE";
    encryption_in_transit: "TLS 1.3";
    sensitive_data: "AES-256 encryption";
    file_uploads: "Virus scanning + type validation";
  };
}
```

### **🔒 PRINCÍPIOS DE SEGURANÇA:**
1. **Princípio do Menor Privilégio**: Usuários têm apenas as permissões necessárias
2. **Defesa em Profundidade**: Múltiplas camadas de segurança
3. **Auditoria Completa**: Log de todas as ações críticas
4. **Validação de Entrada**: Sanitização de todos os inputs
5. **Criptografia End-to-End**: Dados protegidos em trânsito e repouso

---

## 🌐 ARQUITETURA DE APIs

### **📡 ESTRUTURA DE APIs PADRONIZADA:**
```typescript
interface APIArchitecture {
  // ✅ AUTENTICAÇÃO - v1
  auth: {
    "POST /api/v1/auth/login": "Autenticação de usuário";
    "POST /api/v1/auth/logout": "Logout e invalidação de sessão";
    "GET /api/v1/auth/me": "Dados do usuário atual";
    "POST /api/v1/auth/refresh": "Renovação de token";
  };

  // ✅ FUNCIONÁRIOS - v1
  funcionarios: {
    "GET /api/v1/funcionarios": "Listar funcionários com filtros";
    "POST /api/v1/funcionarios": "Criar novo funcionário";
    "GET /api/v1/funcionarios/[id]": "Obter funcionário específico";
    "PUT /api/v1/funcionarios/[id]": "Atualizar funcionário";
    "DELETE /api/v1/funcionarios/[id]": "Inativar funcionário";
    "POST /api/v1/funcionarios/[id]/foto": "Upload de foto";
  };

  // ✅ REGISTRO DE PONTO - v1
  ponto: {
    "GET /api/v1/ponto/registros": "Listar registros com filtros";
    "POST /api/v1/ponto/registros": "Criar registro de ponto";
    "PUT /api/v1/ponto/registros/[id]": "Corrigir registro";
    "GET /api/v1/ponto/espelho": "Espelho de ponto";
  };

  // ✅ RELATÓRIOS - v1
  relatorios: {
    "GET /api/v1/relatorios/ponto": "Relatório de pontos";
    "GET /api/v1/relatorios/funcionarios": "Relatório de funcionários";
    "GET /api/v1/relatorios/epis": "Relatório de EPIs";
    "POST /api/v1/relatorios/export": "Exportar relatórios";
  };

  // ✅ BIOMETRIA - v1
  biometria: {
    "GET /api/v1/biometria/dispositivos": "Listar dispositivos";
    "POST /api/v1/biometria/sincronizar": "Sincronizar templates";
    "GET /api/v1/biometria/status": "Status dos dispositivos";
  };
}
```

### **🔧 PADRÕES DE API UNIFICADOS:**
- **RESTful**: Seguindo convenções REST rigorosamente
- **Versionamento**: `/api/v1/` obrigatório para todas as APIs
- **Rate Limiting**: Proteção contra abuso por usuário/IP
- **Paginação**: Cursor-based para performance otimizada
- **Filtros**: Query parameters padronizados
- **Validação**: Zod schemas para entrada e saída
- **Documentação**: OpenAPI/Swagger automático
- **Autenticação**: Bearer JWT obrigatório
- **Formato de Erro**: Padronizado com código, mensagem e timestamp
- **Content-Type**: `application/json` obrigatório

---

## 📱 ARQUITETURA FRONTEND

### **⚛️ COMPONENTES E ESTADO:**
```typescript
interface FrontendArchitecture {
  component_structure: {
    atomic_design: "Atoms → Molecules → Organisms → Templates → Pages";
    ui_components: "Radix UI primitives + shadcn/ui";
    feature_components: "Organizados por domínio de negócio";
    layout_components: "Header, Sidebar, Navigation, Footer";
  };
  
  state_management: {
    global_state: "Zustand stores por domínio";
    server_state: "React Query para cache de API";
    form_state: "React Hook Form + Zod";
    url_state: "Next.js router para navegação";
  };
  
  styling: {
    framework: "Tailwind CSS v3";
    design_system: "Tokens customizados";
    responsive: "Mobile-first approach";
    themes: "Light/Dark mode support";
  };
}
```

### **🎨 DESIGN SYSTEM:**
```css
/* Cores Primárias */
:root {
  --color-primary: #4fbdba;      /* RLPONTO Teal */
  --color-secondary: #667eea;    /* Accent Blue */
  --color-success: #10b981;      /* Green */
  --color-warning: #f59e0b;      /* Amber */
  --color-error: #ef4444;        /* Red */
  --color-info: #3b82f6;         /* Blue */
}

/* Tipografia */
.font-primary { font-family: 'Geist Sans', sans-serif; }
.font-mono { font-family: 'Geist Mono', monospace; }

/* Espaçamento (8px grid) */
.space-1 { margin: 0.5rem; }    /* 8px */
.space-2 { margin: 1rem; }      /* 16px */
.space-3 { margin: 1.5rem; }    /* 24px */
.space-4 { margin: 2rem; }      /* 32px */
```

---

## 🚀 ARQUITETURA DE DEPLOYMENT

### **☁️ INFRAESTRUTURA:**
```yaml
# Docker Compose para desenvolvimento
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=******************************/rlponto
      - NEXTAUTH_SECRET=your-secret
    depends_on:
      - db
      - redis
  
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=rlponto
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

### **📊 MONITORAMENTO:**
- **Performance**: Vercel Analytics + Core Web Vitals
- **Errors**: Sentry para tracking de erros
- **Logs**: Structured logging com Winston
- **Uptime**: Pingdom para monitoramento 24/7
- **Database**: PostgreSQL slow query log

### **🔄 CI/CD:**
```yaml
# GitHub Actions
name: Deploy
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run build
      - run: npm run test
      - run: npx prisma migrate deploy
      - name: Deploy to Server
        run: |
          # Deploy via SSH to server with Systemd
          ssh user@server "cd /opt/rlponto && git pull && npm ci && npm run build"
          ssh user@server "sudo systemctl restart rlponto.service"
```

---

## 📈 PERFORMANCE E ESCALABILIDADE

### **⚡ OTIMIZAÇÕES:**
- **Next.js**: Server Components + Static Generation
- **Database**: Índices otimizados + connection pooling
- **Caching**: Redis para sessões + React Query
- **Images**: Next.js Image optimization
- **Bundle**: Code splitting automático
- **CDN**: Assets servidos via CDN

### **📊 MÉTRICAS DE PERFORMANCE:**
- **LCP**: < 2.5s (Largest Contentful Paint)
- **FID**: < 100ms (First Input Delay)
- **CLS**: < 0.1 (Cumulative Layout Shift)
- **TTFB**: < 200ms (Time to First Byte)
- **Database**: Queries < 100ms (95th percentile)

---

## 🚀 DEPLOYMENT E RESILIÊNCIA

### **🔧 AUTO-START ROBUSTO - SYSTEMD SERVICE**

#### **🎯 Objetivo Crítico**
Implementar um mecanismo de auto-start robusto que garanta que a aplicação Next.js inicie automaticamente e se mantenha rodando, independentemente de reinicializações do sistema, evitando erros "502 Bad Gateway".

#### **⚙️ Configuração do Serviço Systemd**
```bash
# Criar arquivo de serviço systemd
sudo tee /etc/systemd/system/rlponto.service > /dev/null << EOF
[Unit]
Description=RLPONTO - Sistema de Controle de Ponto Eletrônico
Documentation=https://github.com/rlponto/docs
After=network.target postgresql.service
Wants=network-online.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/rlponto
ExecStart=/usr/bin/npm start
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Environment Variables
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=DATABASE_URL=postgresql://rlponto:rlponto123@localhost:5432/rlponto
Environment=AUTH_URL=http://***********
Environment=AUTH_SECRET=rlponto-auth-secret-2025

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=rlponto

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/opt/rlponto

[Install]
WantedBy=multi-user.target
EOF
```

#### **🔄 Comandos de Gerenciamento**
```bash
# Recarregar configurações do systemd
sudo systemctl daemon-reload

# Habilitar auto-start no boot
sudo systemctl enable rlponto.service

# Iniciar serviço
sudo systemctl start rlponto.service

# Verificar status
sudo systemctl status rlponto.service

# Ver logs em tempo real
sudo journalctl -u rlponto.service -f

# Reiniciar serviço
sudo systemctl restart rlponto.service

# Parar serviço
sudo systemctl stop rlponto.service

# Desabilitar auto-start
sudo systemctl disable rlponto.service
```

#### **✅ Validações Obrigatórias**
```bash
# 1. Verificar se auto-start está habilitado
systemctl is-enabled rlponto.service
# Resultado esperado: "enabled"

# 2. Verificar status do serviço
systemctl status rlponto.service
# Resultado esperado: "active (running)"

# 3. Testar aplicação localmente
curl -I http://localhost:3000
# Resultado esperado: HTTP/1.1 200 OK ou 307 Temporary Redirect

# 4. Testar acesso externo
curl -I http://***********
# Resultado esperado: HTTP/1.1 200 OK via proxy Nginx

# 5. TESTE CRÍTICO - Reinicialização completa
sudo reboot
# Aguardar 2-3 minutos após reinicialização e testar:
curl -I http://***********
# Resultado esperado: Aplicação funcionando SEM intervenção manual
```

#### **🔍 Troubleshooting**
```bash
# Verificar logs de erro
sudo journalctl -u rlponto.service --since "1 hour ago" -p err

# Verificar dependências
sudo systemctl list-dependencies rlponto.service

# Verificar porta em uso
sudo ss -tlnp | grep :3000

# Verificar processo
sudo ps aux | grep npm

# Testar configuração do serviço
sudo systemd-analyze verify /etc/systemd/system/rlponto.service

# Verificar tempo de inicialização
sudo systemd-analyze blame | grep rlponto
```

#### **📋 Checklist de Resiliência**
- ✅ Serviço systemd criado em `/etc/systemd/system/rlponto.service`
- ✅ Auto-start habilitado com `systemctl enable`
- ✅ Dependências de rede configuradas (`After=network.target`)
- ✅ Dependência do PostgreSQL configurada
- ✅ Restart automático configurado (`Restart=always`)
- ✅ Timeout de inicialização configurado
- ✅ Variáveis de ambiente definidas
- ✅ Logs centralizados no systemd journal
- ✅ Segurança básica configurada
- ✅ Testado com reinicialização completa
- ✅ Aplicação inicia automaticamente após reboot
- ✅ Nginx consegue conectar sem erro 502

#### **⚠️ Anti-Padrões a Evitar**
- ❌ **NÃO** usar apenas PM2 sem integração com systemd
- ❌ **NÃO** deixar aplicação dependente de `pm2 startup` manual
- ❌ **NÃO** configurar auto-start apenas via crontab `@reboot`
- ❌ **NÃO** usar scripts de inicialização em `/etc/rc.local`
- ❌ **NÃO** deixar aplicação sem restart automático
- ❌ **NÃO** ignorar dependências de rede no systemd

#### **🎯 Resultado Esperado**
Após a implementação correta:
- ✅ Sistema inicia automaticamente após qualquer reinicialização
- ✅ Não apresenta 502 Bad Gateway em condições normais
- ✅ Se recupera automaticamente de falhas da aplicação
- ✅ Funciona independentemente de intervenção manual
- ✅ Mantém logs centralizados para monitoramento
- ✅ Respeita dependências de serviços (PostgreSQL, rede)

**🚨 CRÍTICO**: Um sistema em produção NUNCA deve depender de intervenção manual para funcionar após reinicializações. A implementação de auto-start robusto é FUNDAMENTAL para a confiabilidade do sistema RLPONTO.
